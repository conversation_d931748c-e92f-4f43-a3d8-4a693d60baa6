import React, { useState, useMemo } from "react";
import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  ColumnDef,
  flexRender,
  Column,
  Row,
  Header,
  Cell,
} from "@tanstack/react-table";
import {
  Card,
  CardContent,
  Badge,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui";
import {
  Edit,
  Eye,
  Calendar,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
} from "lucide-react";
import { format, parseISO, isAfter } from "date-fns";
import { getPriorityConfig as getPriorityConfigShared } from "@/shared/utils/status";

// Task interface for the table
interface Task {
  id: string;
  title: string;
  description: string;
  status: "Not Started" | "In Progress" | "Complete" | "Overdue";
  dueDate: string;
  priority: "Low" | "Medium" | "High";
  projectTag: string;
  assignedTo: {
    id: string;
    name: string;
    avatar: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface TaskTableProps {
  tasks: Task[];
  onTaskEdit?: (task: Task) => void;
  onTaskView?: (task: Task) => void;
  onTaskClick?: (task: Task) => void;
  onCreateTask?: () => void;
  isLeader?: boolean;
  title?: string;
  description?: string;
}

export const TaskTable: React.FC<TaskTableProps> = ({
  tasks,
  onTaskEdit,
  onTaskView,
  onTaskClick,
}) => {
  // Check if task is overdue
  const isOverdue = (dueDate: string, status: string) => {
    if (status === "Complete") return false;
    return isAfter(new Date(), parseISO(dueDate));
  };

  // Update task status to Overdue if past due date
  const tasksWithOverdueCheck = useMemo(() => {
    return tasks.map((task) => {
      if (isOverdue(task.dueDate, task.status) && task.status !== "Complete") {
        return { ...task, status: "Overdue" as const };
      }
      return task;
    });
  }, [tasks]);

  // Get priority configuration from shared utilities
  const getPriorityConfig = (priority: string) => {
    const config = getPriorityConfigShared(priority);
    return { color: config.badgeColor, icon: config.icon };
  };

  // Get status configuration
  const getStatusConfig = (status: string) => {
    switch (status) {
      case "Complete":
        return { color: "bg-green-100 text-green-700" };
      case "In Progress":
        return { color: "bg-blue-100 text-blue-700" };
      case "Overdue":
        return { color: "bg-red-100 text-red-700" };
      case "Not Started":
        return { color: "bg-slate-100 text-slate-700" };
      default:
        return { color: "bg-slate-100 text-slate-700" };
    }
  };

  // Table columns definition
  const columns = useMemo<ColumnDef<Task>[]>(
    () => [
      {
        accessorKey: "title",
        header: ({ column }: { column: Column<Task, unknown> }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-auto p-0 font-semibold text-slate-700 hover:text-slate-900"
          >
            Task Title
            {column.getIsSorted() === "asc" ? (
              <ArrowUp className="ml-2 h-4 w-4" />
            ) : column.getIsSorted() === "desc" ? (
              <ArrowDown className="ml-2 h-4 w-4" />
            ) : (
              <ArrowUpDown className="ml-2 h-4 w-4" />
            )}
          </Button>
        ),
        cell: ({ row }: { row: Row<Task> }) => (
          <div className="max-w-[300px]">
            <div className="font-medium text-slate-900 truncate">
              {row.original.title}
            </div>
            <div className="text-sm text-slate-500 truncate">
              {row.original.description}
            </div>
          </div>
        ),
      },
      {
        accessorKey: "priority",
        header: ({ column }: { column: Column<Task, unknown> }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-auto p-0 font-semibold text-slate-700 hover:text-slate-900"
          >
            Priority
            {column.getIsSorted() === "asc" ? (
              <ArrowUp className="ml-2 h-4 w-4" />
            ) : column.getIsSorted() === "desc" ? (
              <ArrowDown className="ml-2 h-4 w-4" />
            ) : (
              <ArrowUpDown className="ml-2 h-4 w-4" />
            )}
          </Button>
        ),
        cell: ({ row }: { row: Row<Task> }) => {
          const priorityConfig = getPriorityConfig(row.original.priority);
          return (
            <div className="flex justify-center">
              <Badge variant="outline" className={priorityConfig.color}>
                {priorityConfig.icon} {row.original.priority}
              </Badge>
            </div>
          );
        },
      },
      {
        accessorKey: "dueDate",
        header: ({ column }: { column: Column<Task, unknown> }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-auto p-0 font-semibold text-slate-700 hover:text-slate-900"
          >
            Due Date
            {column.getIsSorted() === "asc" ? (
              <ArrowUp className="ml-2 h-4 w-4" />
            ) : column.getIsSorted() === "desc" ? (
              <ArrowDown className="ml-2 h-4 w-4" />
            ) : (
              <ArrowUpDown className="ml-2 h-4 w-4" />
            )}
          </Button>
        ),
        cell: ({ row }: { row: Row<Task> }) => {
          const dueDate = parseISO(row.original.dueDate);
          const overdue = isOverdue(row.original.dueDate, row.original.status);
          return (
            <div className="flex items-center space-x-2">
              <Calendar className="w-4 h-4 text-slate-400" />
              <span
                className={`text-sm ${
                  overdue ? "text-red-600 font-medium" : "text-slate-700"
                }`}
              >
                {format(dueDate, "MMM dd, yyyy")}
              </span>
            </div>
          );
        },
      },
      {
        accessorKey: "projectTag",
        header: "Category",
        cell: ({ row }: { row: Row<Task> }) => (
          <Badge variant="secondary" className="bg-slate-100 text-slate-700">
            {row.original.projectTag}
          </Badge>
        ),
      },
      {
        accessorKey: "assignedTo",
        header: "Assigned To",
        cell: ({ row }: { row: Row<Task> }) => (
          <div className="flex items-center space-x-2">
            <Avatar className="w-6 h-6">
              <AvatarImage
                src={row.original.assignedTo.avatar}
                alt={row.original.assignedTo.name}
              />
              <AvatarFallback className="bg-slate-100 text-slate-600 text-xs">
                {row.original.assignedTo.name
                  .split(" ")
                  .map((n: string) => n[0])
                  .join("")}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm text-slate-700">
              {row.original.assignedTo.name}
            </span>
          </div>
        ),
      },
      {
        accessorKey: "status",
        header: ({ column }: { column: Column<Task, unknown> }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-auto p-0 font-semibold text-slate-700 hover:text-slate-900"
          >
            Status
            {column.getIsSorted() === "asc" ? (
              <ArrowUp className="ml-2 h-4 w-4" />
            ) : column.getIsSorted() === "desc" ? (
              <ArrowDown className="ml-2 h-4 w-4" />
            ) : (
              <ArrowUpDown className="ml-2 h-4 w-4" />
            )}
          </Button>
        ),
        cell: ({ row }: { row: Row<Task> }) => {
          const statusConfig = getStatusConfig(row.original.status);
          return (
            <Badge className={statusConfig.color}>{row.original.status}</Badge>
          );
        },
      },
      {
        id: "actions",
        header: "Actions",
        cell: ({ row }: { row: Row<Task> }) => (
          <div className="flex items-center space-x-2">
            {onTaskView && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onTaskView(row.original)}
                className="border-slate-300 text-slate-700 hover:bg-slate-50"
              >
                <Eye className="w-4 h-4 mr-1" />
                View
              </Button>
            )}
            {onTaskEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onTaskEdit(row.original)}
                className="border-slate-300 text-slate-700 hover:bg-slate-50"
              >
                <Edit className="w-4 h-4 mr-1" />
                Edit
              </Button>
            )}
          </div>
        ),
      },
    ],
    [onTaskEdit, onTaskView]
  );

  // Filter tasks based on all filters
  const filteredTasks = useMemo(() => {
    let filtered = tasksWithOverdueCheck;

    // Status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter((task) => task.status === statusFilter);
    }

    // Priority filter
    if (priorityFilter !== "all") {
      filtered = filtered.filter((task) => task.priority === priorityFilter);
    }

    // Category filter
    if (categoryFilter !== "all") {
      filtered = filtered.filter((task) => task.projectTag === categoryFilter);
    }

    // Due date filter
    if (dueDateFilter !== "all") {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);
      const nextWeek = new Date(today);
      nextWeek.setDate(nextWeek.getDate() + 7);

      filtered = filtered.filter((task) => {
        const taskDueDate = parseISO(task.dueDate);
        switch (dueDateFilter) {
          case "overdue":
            return isOverdue(task.dueDate, task.status);
          case "today":
            return taskDueDate >= today && taskDueDate < tomorrow;
          case "week":
            return taskDueDate >= today && taskDueDate <= nextWeek;
          default:
            return true;
        }
      });
    }

    return filtered;
  }, [
    tasksWithOverdueCheck,
    statusFilter,
    priorityFilter,
    categoryFilter,
    dueDateFilter,
  ]);

  const table = useReactTable({
    data: tasksWithOverdueCheck,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    initialState: {
      pagination: {
        pageSize: 15,
      },
    },
  });

  return (
    <div className="h-full flex flex-col">
      {/* Task Table */}
      <Card className="flex-1 border-slate-200 shadow-sm bg-white">
        <CardContent className="p-0">
          <div className="max-h-[calc(100vh-300px)]">
            <Table>
              <TableHeader>
                <TableRow className="border-slate-200 bg-gradient-to-r from-slate-50 to-slate-100">
                  {table.getHeaderGroups().map((headerGroup) =>
                    headerGroup.headers.map((header: Header<Task, unknown>) => (
                      <TableHead
                        key={header.id}
                        className="text-slate-700 font-medium py-3 px-4 text-sm border-r border-slate-200 last:border-r-0"
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    ))
                  )}
                </TableRow>
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row: Row<Task>, index) => (
                    <TableRow
                      key={row.id}
                      className={`hover:bg-blue-50 border-slate-200 cursor-pointer transition-colors duration-200 ${
                        index % 2 === 0 ? "bg-white" : "bg-slate-50/30"
                      }`}
                      onClick={() => onTaskClick && onTaskClick(row.original)}
                    >
                      {row
                        .getVisibleCells()
                        .map((cell: Cell<Task, unknown>) => (
                          <TableCell
                            key={cell.id}
                            className="py-3 px-4 text-sm border-r border-slate-100 last:border-r-0"
                          >
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext()
                            )}
                          </TableCell>
                        ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center text-slate-500 text-sm"
                    >
                      No tasks found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
