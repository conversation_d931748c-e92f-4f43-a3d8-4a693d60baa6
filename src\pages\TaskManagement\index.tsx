import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import {
  TaskTable,
  TaskDetailModal,
  MinimizedTaskBoard,
  TaskStatsCards,
  CreateTaskWithMilestoneModal,
} from "@/components/tasks";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { toast } from "sonner";
import {
  Table as TableIcon,
  Kanban,
  Plus,
  BarChart3,
  Users,
  Filter,
  Target,
} from "lucide-react";

// Milestone and Task interfaces - compatible with ProjectDetail types
interface Milestone {
  id: string;
  name: string;
  description: string;
  deadline: string;
  status: "Not Started" | "In Progress" | "Completed" | "Overdue";
  progress: number;
  tasks: Task[];
}

// Enhanced Task interface with milestone support - keeping existing status values for compatibility
interface Task {
  id: string;
  title: string;
  description: string;
  status: "Not Started" | "In Progress" | "Complete" | "Overdue";
  dueDate: string;
  priority: "Low" | "Medium" | "High";
  projectTag: string;
  milestoneId?: string; // Link to milestone
  assignedTo: {
    id: string;
    name: string;
    avatar: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

// Mock milestones data
const mockMilestones: Milestone[] = [
  {
    id: "milestone-1",
    name: "Project Setup & Planning",
    description: "Initial project setup, planning, and team coordination",
    deadline: "2024-02-28T17:00:00Z",
    status: "In Progress",
    progress: 75,
    tasks: [],
  },
  {
    id: "milestone-2",
    name: "Core Development",
    description: "Main development phase including backend and frontend work",
    deadline: "2024-04-30T17:00:00Z",
    status: "Not Started",
    progress: 0,
    tasks: [],
  },
  {
    id: "milestone-3",
    name: "Testing & Deployment",
    description: "Testing, optimization, and deployment preparation",
    deadline: "2024-06-15T17:00:00Z",
    status: "Not Started",
    progress: 0,
    tasks: [],
  },
];

// Enhanced mock data with milestone assignments and updated statuses
const mockTasks: Task[] = [
  {
    id: "1",
    title: "Implement User Authentication",
    description:
      "Set up JWT-based authentication system with login/logout functionality",
    status: "In Progress",
    dueDate: "2024-02-15T17:00:00Z",
    priority: "High",
    projectTag: "Backend API",
    milestoneId: "milestone-2",
    assignedTo: {
      id: "user1",
      name: "Sarah Chen",
      avatar: "",
      email: "<EMAIL>",
    },
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-20T14:30:00Z",
  },
  {
    id: "2",
    title: "Design Dashboard UI",
    description: "Create responsive dashboard layout with charts and widgets",
    status: "Complete",
    dueDate: "2024-01-30T17:00:00Z",
    priority: "Medium",
    projectTag: "Frontend",
    milestoneId: "milestone-1",
    assignedTo: {
      id: "user2",
      name: "Michael Rodriguez",
      avatar: "",
      email: "<EMAIL>",
    },
    createdAt: "2024-01-10T09:00:00Z",
    updatedAt: "2024-01-28T16:45:00Z",
  },
  {
    id: "3",
    title: "Database Migration",
    description: "Migrate legacy database to new PostgreSQL instance",
    status: "Overdue",
    dueDate: "2024-01-20T17:00:00Z",
    priority: "High",
    projectTag: "Database",
    milestoneId: "milestone-1",
    assignedTo: {
      id: "user3",
      name: "Emily Johnson",
      avatar: "",
      email: "<EMAIL>",
    },
    createdAt: "2024-01-05T08:00:00Z",
    updatedAt: "2024-01-18T11:20:00Z",
  },
  {
    id: "4",
    title: "API Documentation",
    description: "Write comprehensive API documentation using OpenAPI/Swagger",
    status: "Not Started",
    dueDate: "2024-02-20T17:00:00Z",
    priority: "Low",
    projectTag: "Documentation",
    milestoneId: "milestone-2",
    assignedTo: {
      id: "user1",
      name: "Sarah Chen",
      avatar: "",
      email: "<EMAIL>",
    },
    createdAt: "2024-01-12T14:00:00Z",
    updatedAt: "2024-01-12T14:00:00Z",
  },
  {
    id: "5",
    title: "Performance Optimization",
    description: "Optimize application performance and reduce load times",
    status: "In Progress",
    dueDate: "2024-02-10T17:00:00Z",
    priority: "Medium",
    projectTag: "Performance",
    milestoneId: "milestone-2",
    assignedTo: {
      id: "user2",
      name: "Michael Rodriguez",
      avatar: "",
      email: "<EMAIL>",
    },
    createdAt: "2024-01-18T11:30:00Z",
    updatedAt: "2024-01-25T09:15:00Z",
  },
  {
    id: "6",
    title: "Mobile App Testing",
    description:
      "Comprehensive testing of mobile application across different devices",
    status: "Not Started",
    dueDate: "2024-02-25T17:00:00Z",
    priority: "Medium",
    projectTag: "Mobile",
    milestoneId: "milestone-3",
    assignedTo: {
      id: "user4",
      name: "David Kim",
      avatar: "",
      email: "<EMAIL>",
    },
    createdAt: "2024-01-20T11:00:00Z",
    updatedAt: "2024-01-20T11:00:00Z",
  },
];

const UserTaskManagement: React.FC = () => {
  const [tasks, setTasks] = useState<Task[]>(mockTasks);
  const [milestones, setMilestones] = useState<Milestone[]>(mockMilestones);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [activeView, setActiveView] = useState<"table" | "kanban">("kanban"); // Default to kanban
  const [selectedMilestoneId, setSelectedMilestoneId] = useState<string>("all"); // For filtering tasks by milestone

  // Role-based permissions (can be made dynamic based on user context)
  const isLeader = true;

  // Calculate milestone progress
  const calculateMilestoneProgress = useCallback(
    (milestoneId: string) => {
      const milestoneTasks = tasks.filter(
        (task) => task.milestoneId === milestoneId
      );
      if (milestoneTasks.length === 0) return 0;

      const completedTasks = milestoneTasks.filter(
        (task) => task.status === "Complete"
      );
      return Math.round((completedTasks.length / milestoneTasks.length) * 100);
    },
    [tasks]
  );

  // Update milestone progress when tasks change
  useEffect(() => {
    setMilestones((prev) =>
      prev.map((milestone) => ({
        ...milestone,
        progress: calculateMilestoneProgress(milestone.id),
        tasks: tasks.filter((task) => task.milestoneId === milestone.id),
      }))
    );
  }, [tasks, calculateMilestoneProgress]);

  // Filter tasks based on selected milestone
  const filteredTasks = useMemo(() => {
    if (selectedMilestoneId === "all") {
      return tasks;
    }
    if (selectedMilestoneId === "none") {
      return tasks.filter((task) => !task.milestoneId);
    }
    return tasks.filter((task) => task.milestoneId === selectedMilestoneId);
  }, [tasks, selectedMilestoneId]);

  // Task event handlers
  const handleTaskEdit = (task: Task) => {
    setSelectedTask(task);
    setIsDetailModalOpen(true);
  };

  const handleTaskView = (task: Task) => {
    setSelectedTask(task);
    setIsDetailModalOpen(true);
  };

  const handleTaskClick = (task: Task) => {
    setSelectedTask(task);
    setIsDetailModalOpen(true);
  };

  const handleCreateTaskClick = () => {
    setIsCreateModalOpen(true);
  };

  // Task creation handler
  const handleCreateTaskSubmit = (
    newTask: Omit<Task, "id" | "createdAt" | "updatedAt">
  ) => {
    const task: Task = {
      ...newTask,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    setTasks((prev) => [task, ...prev]);
    setIsCreateModalOpen(false);

    // Show success toast
    toast.success("Task created successfully!", {
      description: `"${task.title}" has been added to your task list.`,
    });
  };

  // Task update handler
  const handleUpdateTask = (updatedTask: Task) => {
    const previousTask = tasks.find((task) => task.id === updatedTask.id);
    const statusChanged =
      previousTask && previousTask.status !== updatedTask.status;

    setTasks((prev) =>
      prev.map((task) =>
        task.id === updatedTask.id
          ? { ...updatedTask, updatedAt: new Date().toISOString() }
          : task
      )
    );
    setSelectedTask(updatedTask);

    // Show appropriate toast based on what changed
    if (statusChanged) {
      toast.success("Task status updated!", {
        description: `"${updatedTask.title}" status changed to ${updatedTask.status}.`,
      });
    } else {
      toast.success("Task updated successfully!", {
        description: `"${updatedTask.title}" has been updated.`,
      });
    }
  };

  // Calculate task statistics based on filtered tasks
  const taskStats = {
    total: filteredTasks.length,
    notStarted: filteredTasks.filter((t) => t.status === "Not Started").length,
    inProgress: filteredTasks.filter((t) => t.status === "In Progress").length,
    completed: filteredTasks.filter((t) => t.status === "Complete").length,
    overdue: filteredTasks.filter((t) => {
      if (t.status === "Complete") return false;
      return new Date() > new Date(t.dueDate);
    }).length,
  };

  // Get unique team members for statistics
  const teamMembers = Array.from(
    new Set(tasks.map((task) => task.assignedTo.id))
  ).length;

  // Get unique project tags
  const projectTags = Array.from(
    new Set(tasks.map((task) => task.projectTag))
  ).length;

  return (
    <div className="h-screen bg-slate-50 flex flex-col overflow-hidden">
      {/* Header */}
      <div className="flex-shrink-0 bg-white border-b border-slate-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-6 py-6">
          {/* Header layout */}
          <div className="space-y-4">
            {/* Title Section */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="min-w-0 flex-1">
                <h1 className="text-2xl font-semibold text-slate-900 tracking-tight truncate">
                  Task Management
                </h1>
                <p className="text-sm text-slate-600 mt-1">
                  Organize, track, and manage your team's work
                </p>
              </div>

              {/* Create Task Button */}
              {isLeader && (
                <div className="flex-shrink-0">
                  <Button
                    onClick={handleCreateTaskClick}
                    className="bg-emerald-600 hover:bg-emerald-700 text-white flex items-center space-x-2 h-10 px-4 text-sm font-medium"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Create Task</span>
                  </Button>
                </div>
              )}
            </div>

            {/* Controls Section */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              {/* Left side: Stats and Milestone Filter */}
              <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                {/* Stats */}
                <div className="flex items-center space-x-6 text-sm">
                  <div className="flex items-center space-x-2">
                    <BarChart3 className="w-4 h-4 text-slate-500" />
                    <span className="text-slate-700 font-medium">
                      {taskStats.total} Tasks
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Users className="w-4 h-4 text-slate-500" />
                    <span className="text-slate-700 font-medium">
                      {teamMembers} Members
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Filter className="w-4 h-4 text-slate-500" />
                    <span className="text-slate-700 font-medium">
                      {projectTags} Projects
                    </span>
                  </div>
                </div>

                {/* Milestone Filter */}
                <div className="flex items-center space-x-2">
                  <Target className="w-4 h-4 text-slate-500" />
                  <Select
                    value={selectedMilestoneId}
                    onValueChange={setSelectedMilestoneId}
                  >
                    <SelectTrigger className="w-[200px] h-9 text-sm">
                      <SelectValue placeholder="Filter by milestone" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Tasks</SelectItem>
                      {milestones.map((milestone) => (
                        <SelectItem key={milestone.id} value={milestone.id}>
                          {milestone.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* View Toggle */}
              <div className="flex items-center space-x-1 bg-slate-100 rounded-lg p-1">
                <Button
                  variant={activeView === "table" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setActiveView("table")}
                  className="h-8 px-3 text-sm font-medium"
                >
                  <TableIcon className="w-4 h-4 mr-2" />
                  <span>Table</span>
                </Button>
                <Button
                  variant={activeView === "kanban" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setActiveView("kanban")}
                  className="h-8 px-3 text-sm font-medium"
                >
                  <Kanban className="w-4 h-4 mr-2" />
                  <span>Kanban</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content Area - Full Height */}
      <div className="flex-1 overflow-hidden">
        {activeView === "table" ? (
          <div className="h-full flex flex-col">
            {/* Stats for Table View */}
            <div className="flex-shrink-0 max-w-7xl mx-auto px-6 py-4">
              <TaskStatsCards
                stats={taskStats}
                teamMembers={teamMembers}
                projectTags={projectTags}
                showExtendedStats={false}
              />
            </div>

            {/* Table View - Full Height */}
            <div className="flex-1 max-w-7xl mx-auto px-6 pb-6 overflow-hidden">
              <TaskTable
                tasks={filteredTasks}
                onTaskEdit={handleTaskEdit}
                onTaskView={handleTaskView}
                onTaskClick={handleTaskClick}
                onCreateTask={handleCreateTaskClick}
                isLeader={isLeader}
              />
            </div>
          </div>
        ) : (
          /* Kanban View - Full Height */
          <div className="h-full bg-slate-50 ">
            <div className="flex justify-center h-full">
              <div className="w-full max-w-8xl px-6 py-6 flex justify-center">
                <MinimizedTaskBoard
                  tasks={filteredTasks}
                  onTaskUpdate={handleUpdateTask}
                  onTaskClick={handleTaskClick}
                  isLeader={isLeader}
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      {/* Task Detail Modal */}
      <TaskDetailModal
        open={isDetailModalOpen}
        onOpenChange={setIsDetailModalOpen}
        task={selectedTask}
        onUpdate={handleUpdateTask}
        isLeader={isLeader}
      />

      {/* Create Task Modal with Milestone Selection */}
      <CreateTaskWithMilestoneModal
        open={isCreateModalOpen}
        onOpenChange={setIsCreateModalOpen}
        onCreate={handleCreateTaskSubmit}
        milestones={milestones}
      />
    </div>
  );
};

export default UserTaskManagement;
